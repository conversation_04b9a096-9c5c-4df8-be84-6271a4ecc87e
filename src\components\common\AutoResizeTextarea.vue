<script setup lang="ts">
import { ref, onMounted, watch, nextTick, defineProps, defineModel, withDefaults } from 'vue'

interface AutoResizeTextareaProps {
  placeholder?: string
  disabled?: boolean
  minHeight?: number
  maxHeight?: number
  focusSave?: boolean
  saveCallBack?: () => void
}

const props = withDefaults(defineProps<AutoResizeTextareaProps>(), {
  placeholder: '请输入',
  disabled: false,
  minHeight: 40,
  maxHeight: 300,
  focusSave: false
})

const model = defineModel<string>()
const textareaRef = ref<HTMLTextAreaElement>()

// 自动调整高度的函数
const adjustHeight = async () => {
  if (!textareaRef.value) return
  
  await nextTick()
  
  const textarea = textareaRef.value
  // 重置高度以获取正确的scrollHeight
  textarea.style.height = 'auto'
  
  const scrollHeight = textarea.scrollHeight
  const minHeight = props.minHeight
  const maxHeight = props.maxHeight
  
  if (scrollHeight <= minHeight) {
    // 内容少于最小高度时，使用最小高度
    textarea.style.height = `${minHeight}px`
    textarea.style.overflowY = 'hidden'
  } else if (scrollHeight <= maxHeight) {
    // 内容在最小和最大高度之间时，自适应高度
    textarea.style.height = `${scrollHeight}px`
    textarea.style.overflowY = 'hidden'
  } else {
    // 内容超过最大高度时，固定最大高度并显示滚动条
    textarea.style.height = `${maxHeight}px`
    textarea.style.overflowY = 'auto'
  }
}

// 监听内容变化
watch(() => model.value, () => {
  adjustHeight()
}, { immediate: true })

// 处理输入事件
const handleInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  model.value = target.value
  adjustHeight()
}



// 组件挂载后初始化高度
onMounted(() => {
  adjustHeight()
})

// 暴露方法供父组件调用（保持与InlineEditor接口兼容）
const getData = (): string => {
  return model.value || ''
}

const setData = (value: string) => {
  model.value = value
  nextTick(() => {
    adjustHeight()
  })
}

defineExpose({
  getData,
  setData
})
</script>

<template>
  <textarea
    ref="textareaRef"
    v-model="model"
    :placeholder="placeholder"
    :disabled="disabled"
    @input="handleInput"
    class="auto-resize-textarea"
  />
</template>

<style scoped>
.auto-resize-textarea {
  width: 100%;
  min-height: v-bind('props.minHeight + "px"');
  max-height: v-bind('props.maxHeight + "px"');
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
  line-height: 1.5;
  resize: none;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.auto-resize-textarea:focus {
  border-color: #409eff;
}

.auto-resize-textarea:disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.auto-resize-textarea::placeholder {
  color: #c0c4cc;
}
</style>
