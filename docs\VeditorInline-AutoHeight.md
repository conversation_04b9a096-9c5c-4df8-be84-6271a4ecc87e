# VeditorInline 自适应高度功能

## 功能概述

VeditorInline 组件新增了自适应高度功能，允许编辑器根据内容动态调整高度，提供更好的用户体验。

## 新增参数

### autoHeight
- **类型**: `boolean`
- **默认值**: `false`
- **描述**: 是否启用自适应高度功能

### maxHeight
- **类型**: `number`
- **默认值**: `300`
- **描述**: 自适应高度的最大值（单位：px），超过此高度将显示滚动条

## 使用方法

### 基础用法

```vue
<template>
  <!-- 固定高度模式（默认） -->
  <VeditorInline 
    v-model="content1" 
    :height="150"
    placeholder="固定高度编辑器"
  />

  <!-- 自适应高度模式 -->
  <VeditorInline 
    v-model="content2" 
    :height="100"
    :auto-height="true"
    :max-height="300"
    placeholder="自适应高度编辑器"
  />
</template>

<script setup>
import { ref } from 'vue'
import VeditorInline from '@/components/editors/VeditorInline.vue'

const content1 = ref('')
const content2 = ref('')
</script>
```

### 高级用法

```vue
<template>
  <!-- 带工具栏的自适应高度编辑器 -->
  <VeditorInline 
    v-model="content" 
    :height="80"
    :auto-height="true"
    :max-height="400"
    :show-toolbar="true"
    placeholder="带工具栏的自适应编辑器"
  />
</template>
```

## 工作原理

### 高度计算流程

1. **内容变化检测**: 监听编辑器内容变化事件
2. **高度计算**: 获取内容实际高度
3. **边界检查**: 确保高度在最小值和最大值之间
4. **样式更新**: 动态更新编辑器容器高度
5. **滚动条控制**: 超过最大高度时显示滚动条

### 关键特性

- **最小高度**: 使用 `height` 参数作为最小高度
- **最大高度**: 使用 `maxHeight` 参数限制最大高度
- **滚动条**: 内容超过最大高度时自动显示滚动条
- **实时更新**: 内容变化时实时调整高度
- **性能优化**: 使用 `setTimeout` 避免频繁计算

## 参数对比

| 参数 | 固定高度模式 | 自适应高度模式 |
|------|-------------|---------------|
| `height` | 编辑器固定高度 | 最小高度 |
| `autoHeight` | `false` | `true` |
| `maxHeight` | 无效 | 最大高度限制 |
| 滚动条 | 内容超出时隐藏 | 超过最大高度时显示 |

## 注意事项

1. **性能考虑**: 自适应高度会在内容变化时进行计算，对于大量文本可能有轻微性能影响
2. **最小高度**: 即使内容很少，编辑器高度也不会小于 `height` 参数设置的值
3. **最大高度**: 建议根据页面布局合理设置 `maxHeight`，避免编辑器过高影响页面结构
4. **兼容性**: 该功能与现有的工具栏、禁用等功能完全兼容

## 测试页面

可以通过访问测试页面来体验自适应高度功能：

```
src/views/test/VeditorAutoHeightTest.vue
```

测试页面包含：
- 固定高度模式对比
- 自适应高度基础功能
- 带工具栏的自适应高度
- 各种测试内容和操作按钮
