import axios from "axios"
import type { AxiosInstance, AxiosRequestConfig } from "axios"
import { ElLoading, ElMessage } from "element-plus"

type LoadingInstanceType = ReturnType<typeof ElLoading.service>
let loadingInstance: LoadingInstanceType | null = null
let requestNum = 0
let error_flag = false
export const createAxiosByinterceptors = ( config?: AxiosRequestConfig ): AxiosInstance => {
  const instance = axios.create({
    timeout: 5000, //超时配置
    // withCredentials: true,  //跨域携带cookie
    ...config, // 自定义配置覆盖基本配置
  })

  // 添加请求拦截器
  instance.interceptors.request.use(
    function (config: any) {
      // 在发送请求之前做些什么
      const { loading = true } = config
      if (loading) addLoading()
      return config
    },
    function (error) {
      return Promise.reject(error)
    }
  )
  // 添加响应拦截器
  instance.interceptors.response.use(
    function (response) {
      // @ts-ignore
      const { loading = true } = response.config
      if (loading) cancelLoading()
      return response.data
    },
    function (error) {
      const { loading = true } = error.config
      if (loading) cancelLoading()
      
      if (!error_flag) {
        error_flag = true
        switchErrorStatus(error)
      }
      return Promise.reject(error)
    }
  )
  return instance
}
// 增加loading 如果pending请求数量等于1，弹出loading, 防止重复弹出
const addLoading = () => {
  requestNum++
  if (requestNum == 1) {
    loadingInstance = ElLoading.service({
      text: "正在努力加载中....",
      background: "rgba(0, 0, 0, 0)",
      customClass: "primary",
    })
  }
}

// 取消loading 如果pending请求数量等于0，关闭loading
const cancelLoading = () => {
  requestNum--
  if (requestNum === 0) loadingInstance?.close()
}

const switchErrorStatus = (error: any) => {
  const url = error.response.headers['x-redirect-url']
  const status = error.status ?? error.response.status
  if (!url) {
    ElMessage.error('服务端异常，3s后跳回首页')
    setTimeout(() => {
      window.open('https://www.endlessorigin.com', '_self')
    }, 3000)
  }
  switch (status) {
    case 302:
      window.open(url, '_self');
      break;
    case 401:
      ElMessage.warning('未登录，3s后跳转到登录页')
      setTimeout(() => {
        window.open(url+`&protocol=${import.meta.env.VITE_APP_HTTP}`, '_self');
      }, 3000);
      break;
    case 403:
      ElMessage.warning('用户无权限，3s后跳转到首页')
      setTimeout(() => {
        window.open(url, '_self');
      }, 3000);
      break;
    default:
      ElMessage.error(error.response.data.message || '服务端异常')
  }
  // 避免以下弹出很多提示框
  setTimeout(() => (error_flag = false), 5000)
}