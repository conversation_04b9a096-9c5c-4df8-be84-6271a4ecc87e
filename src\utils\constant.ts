import router from "@/router"
import { userInfoStore } from "@/stores/userInfo"
import { ref } from "vue"

export enum prjForm {
  video = 1,
  text = 2,
  all = 3, // getList时传空为拿全部
}
export const formDict: { [key: string]: string } = {
  视频: "1",
  文稿: "2",
}
export enum prjState {
  default = "",
  draft = "0",
  wait2examine = "1",
  examining = "2",
  back = "3",
  withdraw = "4",
  publish = "5",
  delete = "6",
}
export const stateDict: { [key: string]: string } = {
  全部状态: "",
  草稿: "0",
  待审核: "1",
  审核中: "2",
  已退回: "3",
  已撤回: "4",
  已发布: "5",
  已删除: "6",
}
export const stateNoDeleteDict: { [key: string]: string } = {
  全部状态: "",
  草稿: "0",
  待审核: "1",
  审核中: "2",
  已退回: "3",
  已撤回: "4",
  已发布: "5",
}
export enum prjType {
  default = "",
  klg = "1",
  prj = "2",
  exam = "3",
  area = "4",
}
export const typeDict: { [key: string]: string } = {
  全部类型: "",
  知识讲解: "1",
  案例学习: "2",
  知识测评: "3",
  领域讲解: "4",
}
export const typeDict_noDefault: { [key: string]: string } = {
  知识讲解: "1",
  案例学习: "2",
  知识测评: "3",
  领域讲解: "4",
}
export const sortDict: { [key: string]: number } = {
  时间降序: 0,
  时间升序: 1,
}
export enum examType {
  blank = "1",
  select = "2",
  judge = "3",
  qa = "4",
}
export const examTypeDict: { [key: string]: string } = {
  填空题: "1",
  选择题: "2",
  判断题: "3",
  问答题: "4",
}
export enum qType {
  what = 0,
  why = 1,
  how = 2,
}
export enum qWeight {
  default = -1,
  personal = 1,
  open = 2,
}
export const qTypeDict: { [key: string]: number } = {
  是什么: qType.what,
  为什么: qType.why,
  怎么做: qType.how,
}
export enum qMode {
  ness = 1,
  refer = 2,
}

export const qModeDict: { [key: string]: number } = {
  必要问题: qMode.ness,
  参考问题: qMode.refer,
}
const menuList = [
  {
    name: "",
    title: "管理项目",
    path: "/home",
    icon: "FolderOpened",
    isActive: false,
    children: [
      {
        name: "",
        title: "我的项目",
        path: "/home/<USER>",
      },
      {
        name: "",
        title: "回收站",
        path: "/home/<USER>",
      },
      {
        name: "",
        title: "项目审核列表",
        path: "/home/<USER>",
      },
    ],
  },
  {
    name: "check",
    title: "查看项目列表",
    path: "/check",
    icon: "Folder",
    isActive: false,
    children: [
      {
        name: "publishedProject",
        title: "已发布项目",
        path: "/home/<USER>",
      },
      {
        name: "explainProjectView",
        title: "讲解项目查看",
        path: "/home/<USER>",
      },
    ],
  },
]
const menuAccessMap: { [key: string]: boolean } = {}
export const getLeftMenu = () => {
  const userinfo = userInfoStore()
  const permissions = userinfo.getPermission()
  const tempMenu = ref<any[]>([])
  const system = permissions.find(
    (serv) => serv.service === import.meta.env.VITE_APP_SERVICE
  )
  if (system && system.access) {
    system.menus.forEach((item) => {
      menuAccessMap[item.menu] = item.access
    })
    console.log("menuAac", menuAccessMap)
    menuList.forEach((firstmenu) => {
      if (!firstmenu.name) {
        tempMenu.value.push(firstmenu)
      } else {
        const tempFstMenu = {
          ...firstmenu,
          children: [] as any[],
        }
        firstmenu.children.forEach((scdmenu) => {
          if (!scdmenu.name) {
            tempFstMenu.children.push(scdmenu)
          } else {
            if (menuAccessMap[scdmenu.name]) {
              tempFstMenu.children.push(scdmenu)
            }
          }
        })
        if (tempFstMenu.children.length !== 0) {
          tempMenu.value.push(tempFstMenu)
        }
      }
    })
  }
  return tempMenu.value
}
// export const pageList = [
//   {
//     title: "创建项目",
//     tag: "createProject",
//     router: "/home/<USER>",
//     icon: "",
//     subPageList: [],
//   },
//   {
//     title: "管理项目",
//     router: "/home",
//     icon: "@/assets/images/lefter/u378.svg",
//     subPageList: [
//       {
//         title: "我的项目",
//         tag: "myproject",
//         router: "/home/<USER>",
//       },
//       {
//         title: "我的项目预览",
//         tag: "myproject",
//         router: "/home/<USER>",
//       },
//       {
//         title: "回收站",
//         tag: "recycle",
//         router: "/home/<USER>",
//       },
//       {
//         title: "项目审核列表",
//         tag: "checking",
//         router: "/home/<USER>",
//       },
//       {
//         title: "项目审核详情",
//         tag: "checking",
//         router: "/home/<USER>",
//       },
//     ],
//   },
//   {
//     title: "查看项目列表",
//     tag: "checkProject",
//     icon: "@/assets/images/lefter/u384.svg",
//     subPageList: [
//       {
//         title: "已发布项目",
//         tag: "prjDisplay",
//         router: "/home/<USER>",
//       },
//       {
//         title: "讲解项目查看",
//         tag: "prjSearch",
//         router: "/home/<USER>",
//       },
//     ],
//   },
//   {
//     title: "权限管理",
//     router: "/home",
//     icon: "@/assets/images/lefter/u392.svg",
//     subPageList: [
//       {
//         title: "用户管理",
//         tag: "userManage",
//         router: "/home/<USER>",
//       },
//       {
//         title: "角色管理",
//         tag: "characterManage",
//         router: "/home/<USER>",
//       },
//       {
//         title: "角色管理",
//         tag: "characterManage",
//         router: "/home/<USER>",
//       },
//       {
//         title: "审批管理",
//         tag: "verifyManage",
//         router: "/home/<USER>",
//       },
//     ],
//   },
// ];
// export const pageList_noCreate = [
//   {
//     title: "项目管理",
//     router: "/home1",
//     // icon: "@/assets/images/lefter/u378.svg",
//     icon: "FolderOpened",
//     subPageList: [
//       {
//         title: "我的项目",
//         tag: "myproject",
//         router: "/home/<USER>",
//       },
//       {
//         title: "回收站",
//         tag: "recycle",
//         router: "/home/<USER>",
//       },
//       {
//         title: "项目审核",
//         tag: "checking",
//         router: "/home/<USER>",
//       },
//     ],
//   },
//   {
//     title: "查看项目",
//     tag: "checkProject",
//     router: "/home2",
//     // icon: "@/assets/images/lefter/u384.svg",
//     icon: "FolderChecked",
//     subPageList: [
//       {
//         title: "已发布项目",
//         tag: "prjDisplay",
//         router: "/home2",
//       },
//       {
//         title: "讲解项目查看",
//         tag: "prjSearch",
//         router: "/home/<USER>",
//       },
//     ],
//   },
//   // {
//   //   title: "权限管理",
//   //   router: "/home3",
//   //   // icon: "@/assets/images/lefter/u392.svg",
//   //   iconPath: "Stamp",
//   //   subPageList: [
//   //     {
//   //       title: "用户管理",
//   //       tag: "userManage",
//   //       router: "/home/<USER>",
//   //     },
//   //     {
//   //       title: "角色管理",
//   //       tag: "characterManage",
//   //       router: "/home/<USER>",
//   //     },
//   //     {
//   //       title: "审批管理",
//   //       tag: "verifyManage",
//   //       router: "/home/<USER>",
//   //     },
//   //   ],
//   // },
// ];

export enum conditionState {
  draft,
  waitingpass,
  nowpassing,
  backbyother,
  backbyme,
  online,
  delete,
}
