import type { examType, qMode, qType } from "@/utils/constant";
import type { Ref } from "vue";

export interface APIResponse {
  success: boolean;
  data: any;
  message: string;
}
export interface basePrjInfo {
  prform: number;
  prname: string;
  prtype: number;
  id: number;
}
export interface klgType {
  code: string;
  title: string;
  type: number;
  choose?: boolean;
}
export interface questionItem {
  questionId: number;
  questionDescription?: string;
  associatedWords?: string;
  keyWords: string;
  questionType: string;
  questionNecessity: number;
  questionWeight: number;
  userName: string;
  createTime: string;
  answerNumber: number;
  questionState: string;
  canDelete: boolean;
}
export interface answerItem {
  answerExplanation: string;
  answerId: number;
  answerStatus: number | null;
  createTime: string;
  keyWords: string;
  klgNumber: number;
  prjTitle: string;
  prjType: number;
  questionType: string;
  taskNumber: number;
  questionDescription?: string;
}
export interface prjInfo4table extends basePrjInfo {
  oprtime: string;
  condition: string;
}
export interface projectTableData {
  title: string;
  prjForm: number;
  creator: string;
  status: number;
  createTime: string;
}
export interface prjInfo2_4table extends basePrjInfo {
  name: string;
  releaseTime: string;
}
export interface recyclePrjInfo extends basePrjInfo {
  deltime: string;
}
// 后端真的不考虑一下统一命名吗...
export interface characterObj {
  name: string;
  count: number;
  id: string;
}
export interface administratorObj {
  number: number;
  id: number;
  title: string;
}
export interface userObj {
  uniqueCode?: string; // no need
  name?: string; // no need
  password?: string; // no need
  showName?: string;
  expiryDate?: string;
  loginTime?: string;
  isValid?: number; // no need
  isAble?: number; // no need
  roleId: number;
  roleName?: string; // no need
  approvalAuthority: string;
  avatar?: string; // no need
  createTime?: string; // no need
  modifiedTime?: string; // no need
  oid: number;
}

export interface imgUrl {
  echoUrl: string;
  commUrl: string;
}
export interface prjInfoType {
  /** 类型是否只读 */
  disable: boolean;
  prjType: "" | "1" | "2" | "3" | "4";
  prjName: string;
  prjAim: string;
  prjGeneral: string;
  // prjTagList: tagType[],
  prjTargetList: tagType[];
  prjAreaList: tagType[];
  prjCover: imgUrl;
  [key: string]: any; // 添加索引签名
}
export interface videoContentType {
  videoCover: imgUrl;
  videoKey: string;
  lecture: lectureType[];
}
export interface videoChapterType extends videoContentType {
  sectionId: number,
  sectionTitle: string;
  videoFlag: string;
  lectureFlag: string;
}
export interface tagType {
  id: string; // (target)klgCode | (tag)id
  name: string;
  isSelected?: boolean;
}
export interface simpleChapterInfo {
  chapterId: number;
  chapterName: string;
  chapterNum: number;
}
export interface lectureType {
  beginning: boolean;
  id: number;
  time: string;
  content: string;
}
export interface examSectionType {
  id: number;
  examAnswer: string;
  examChoices: string[];
  examExplanation: string;
  examTitle: string;
  examType: examType;
}
export interface textSection {
  sectionNum: number;
  sectionTitle: string;
  prText: string;
  sectionId: number;
  sectionContent: examSectionType[];
  htmlContent:string;
}
export interface questionType {
  qId: number;
  isValid?: boolean;
  relatedText: string;
  qContent: string;
  qType: qType; // 是什么 | 为什么
  qMode: qMode; // 必要 | 参考
  klg: tagType[];
  explanation: string;
}
export interface questionType2sort4exam extends questionType {
  contentId: number;
}
export interface examineType {
  link: string;
  auditor: string;
  result: boolean;
  opinion: string;
}
export interface taskType {
  secId: number;
  taskId: number;
  taskKey: string;
  taskProcess: Ref;
}

export interface sectionType {
  sectionTitle: string;
  sectionContent: string;
}
