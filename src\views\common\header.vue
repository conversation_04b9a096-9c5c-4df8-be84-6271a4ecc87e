<script setup lang="ts">
import { onMounted, ref } from "vue"
import { userInfoStore } from "@/stores/userInfo"
import { docCookies } from "@/utils/func"
import { ElMessage } from "element-plus"
import { routerPushSystem } from "@/utils/jump";
import { System } from "@/enum/system";
import { logoutApi } from "@/apis/path/userinfo";

const userinfo = userInfoStore()

// 个人空间
const perurl = import.meta.env.VITE_APP_CHAMP_URL  + "/userspace"
const learningHistory = import.meta.env.VITE_APP_CHAMP_URL + '/learnStatistics'
// 账号资料
const accurl = import.meta.env.VITE_APP_YOUTH_URL  + "/user/information"
// 后台管理
let backurl = null

const backendManagement = ref(false)

const avatar = userinfo.getAvatar()
const permissions = userinfo.getPermission()
const admin = permissions.find((system) => system.service === "admin")
if (admin && admin.access) {
  backendManagement.value = true
  backurl = admin.url
}

// 登出
const logout = async () => {
  try {
    const res = await logoutApi()
    if (res.success) {
      ElMessage.success("退出成功")
    }
  } catch (error) {
    console.error(error)
  }
}

const routerPush = (targetRoute: string) => {
  if (targetRoute === undefined) {
    ElMessage.error("路由跳转失败")
  } else {
    setTimeout(() => {
      window.open(targetRoute, "_self")
    }, 0)
  }
}

</script>

<template>
  <div class="header">
    <span class="left-wrapper">
      <span class="logo">
        <img src="@/assets/logo.png" style="height: 60px" />
      </span>
      <span class="title">
        <span>项目中心</span>
      </span>
      <span class="pusher">
        <span>
          <span class="pusher-item" @click="routerPushSystem(System.champaign)">
            <img
              class="pusher-item-img"
              src="@/assets/images/common/u429.svg"
            />
            <span>学习站</span>
          </span>
        </span>
        <span>
          <span class="pusher-item" @click="routerPushSystem(System.dutchman)" style="margin-left: 15px">
            <img
              class="pusher-item-img"
              src="@/assets/images/common/u432.svg"
            />
            <span>问题中心</span>
          </span>
        </span>
      </span>
    </span>
    <span class="avatar-wrapper">
      <el-dropdown popper-class="primary">
        <img class="avatar" :src="avatar" style="width: 35px" />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item 
              @click="routerPush(perurl)"
              >个人空间
            </el-dropdown-item>
            <el-dropdown-item 
              @click="routerPush(learningHistory)"
              >学习历史
            </el-dropdown-item>
            <el-dropdown-item
              @click="routerPush(accurl)"
              >账号资料
            </el-dropdown-item>
            <el-dropdown-item
              v-if="backendManagement"
              @click="routerPush(backurl)"
              >后台管理
            </el-dropdown-item>
            <el-dropdown-item 
              @click="logout">
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </span>
  </div>
</template>

<style scoped>
.header {
  font-family: var(--text-family) !important;
  height: 60px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  background-color: white;
  /*position: relative;*/
  /*z-index: 3000;*/
  .left-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    color: var(--color-primary);

    .logo {
      width: 106px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;

      /*padding: 10px 0;*/
      .pic {
        width: 60px;
      }
    }

    .title {
      font-size: 20px;
      font-weight: 600;
      color: var(--color-primary);
    }
    .pusher {
      display: flex;
      flex-direction: row;
      height: 100%;
      align-items: center;
      justify-content: center;
      margin-left: 30px;
      .pusher-item {
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--color-deep);
        font-size: 14px;
        margin-left: 40px;
        &:hover {
          font-weight: 600;
          cursor: pointer;
        }
        .pusher-item-img {
          margin-right: 5px;
        }
      }
    }
  }

  .avatar-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    font-family: var(--font-family-text);

    .avatar {
      margin-left: 79px;
      margin-right: 65px;
      border-radius: 10px;
      height: 35px;
      width: 35px;
      cursor: pointer;

      &:focus-visible {
        outline: none !important;
      }
    }
  }
}
</style>
