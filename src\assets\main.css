@import './base.css';
@import './reset.css';
#app {
  min-height: 100vh;
  min-width: 100vw;
  background-color: #ffffff;
  /* position: relative; */
  ::selection{ background:#1973cb; color:#ffffff;}
}
::-webkit-scrollbar {
  position: absolute;
  /*滚动条整体样式*/
  width: 5px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  background: var(--color-grey);
}
