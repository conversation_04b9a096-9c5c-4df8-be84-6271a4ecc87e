import { createApp } from "vue";
import { createPinia } from "pinia";
import "./assets/main.css";
import "element-plus/dist/index.css";
import piniaPersist from "pinia-plugin-persist";
// import CKEditor from "@ckeditor/ckeditor5-vue"

import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import App from "./App.vue";
import ElementPlus from "element-plus";
import router from "./router";

const app = createApp(App);

app.use(router);

app.mount("#app");

const pinia = createPinia();
pinia.use(piniaPersist);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
// @ts-ignore
// app.use(CKEditor)
app.use(ElementPlus);
// app.use(createPinia())
app.use(pinia); // use加了持久化存储的pinia
