<script setup lang="ts">
import {onMounted, ref} from "vue";
import {useRoute} from "vue-router";
import PrjPreview from "@/views/projectPreview/components/prjPreview.vue";

const route = useRoute();
const curPrjId = ref();
const curPrjForm = ref();
onMounted(() => {
  curPrjId.value = parseInt(route.query.prjId as string);
  curPrjForm.value = route.query.prjForm;
})
</script>

<template>
  <prj-preview :prj-id="curPrjId" :prj-form="curPrjForm"></prj-preview>
</template>

<style scoped>

</style>