<script setup lang="ts">
import ClassicEditor from "@/components/editors/Veditor.vue";
import {
  inject,
  reactive,
  type Ref,
  ref,
  watch,
  onMounted,
  onUnmounted,
} from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import {
  getTextSection,
  type params4submitTextSection,
  submitTextSection,
} from "@/apis/path/createProject";
import { emitter } from "@/utils/emitter";
import { Event } from "@/types/event";
import {
  convertLongUrlMarkdownImages,
  convertImgTagLongUrls,
  convertLanguageMathToScript,
} from "@/utils/latexUtils";

import { markdownToHtml, encodeHTML } from "@/utils/mdTohtml";
let editorRef = ref();
// 本组件进行前后端通信
const emits = defineEmits(["save:chapter"]);
const props = defineProps({
  prjId: Number,
  chapter: Object,
  showTitle: {
    type: Boolean,
    default: false,
  },
});

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive({
  sectionId: -1,
  sectionTitle: "",
  sectionNum: 0,
  prText: "",
});
const rules = reactive<FormRules<typeof ruleForm>>({
  sectionTitle: [{ required: true, message: "请输入标题", trigger: "blur" }],
  prText: [{ required: true, message: "请输入内容", trigger: "blur" }],
});

const initData = () => {
  getTextSection(ruleForm.sectionId).then((res) => {
    if (res.success) {
      const textSection = res.data.wordsContent.projectSections[0];
      ruleForm.sectionTitle = textSection.sectionTitle;
      ruleForm.prText = textSection.prText;
    } else {
      ElMessage.error(res.message);
    }
  });
};

// 提交
const saveSubmitChapter = (): Promise<Boolean> => {
  console.log("editorRef", editorRef.value.getData());
  return new Promise((resolve, reject) => {
    // 构造参数
    const param: params4submitTextSection = {
      projectId: props.prjId,
      projectSections: [
        {
          sectionId: ruleForm.sectionId,
          sectionNum: ruleForm.sectionNum,
          sectionTitle: ruleForm.sectionTitle,
          prText: convertLongUrlMarkdownImages(editorRef.value.getData()),
          sectionContent: [],
          // htmlContent: markdownToHtml(
          //   (convertLongUrlMarkdownImages(ruleForm.prText))
          // ),
          htmlContent: convertLanguageMathToScript(
            convertImgTagLongUrls(editorRef.value.getHtml())
          ),
        },
      ],
    };

    // 调用 submitTextSection 并处理结果
    submitTextSection(param)
      .then((res) => {
        if (res.success) {
          resolve(true);
        } else {
          ElMessage.error(res.message);
          resolve(false);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};

// 提交 or 草稿
const handleSaveChapter = (submit?: boolean) => {
  return new Promise<Boolean>((resolve, reject) => {
    if (!ruleFormRef.value) {
      reject(new Error("ruleFormRef is not defined"));
      return;
    }
    if (submit) {
      console.log("submit", submit);
      ruleFormRef.value.validate((valid) => {
        if (valid) {
          saveSubmitChapter()
            .then(() => {
              resolve(true);
            })
            .catch((error) => {
              reject(error);
            });
        }
      });
    } else {
      saveSubmitChapter()
        .then(() => {
          resolve(true);
        })
        .catch((error) => {
          reject(error);
        });
    }
  });
};

const handleSave = (param: { status: number; id: number }) => {
  console.log("param", param, ruleForm.sectionId);
  if (param.id == ruleForm.sectionId) {
    handleSaveChapter(true).then(() => {
      emits("save:chapter", param);
    });
  }
};

watch(
  () => props,
  (newValue, oldValue) => {
    Object.assign(ruleForm, newValue.chapter);
    console.log("chapter", ruleForm);
    // 检查 sectionId 是否为 -1，如果是，则不执行 initData
  },
  { deep: true, immediate: true }
);
const Tryimg =
  '<img src="https://store.endlessorigin.com/2025-06-17/95bab49af9124e1bbcf4976cdf68bc33_Snipaste20230720100547.png?q-sign-algorithm=sha1&amp;q-ak=AKIDGfw30pUz2FayQmDmL3sqocGl5Ljy2whI&amp;q-sign-time=1752130917%3B1752174117&amp;q-key-time=1752130917%3B1752174117&amp;q-header-list=host&amp;q-url-param-list=&amp;q-signature=9d5929ee9921f25cc8be5f65a9546c38e4683ebe" alt="Snipaste20230720100547.png">';
onMounted(() => {
  emitter.on(Event.SAVE_CHAPTER, handleSave);
});
onUnmounted(() => {
  emitter.off(Event.SAVE_CHAPTER, handleSave);
});
</script>
<template>
  <div class="text-content-wrapper">
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      style="width: 100%"
    >
      <el-form-item
        prop="sectionTitle"
        style="width: 100%"
        v-if="props.showTitle"
      >
        <el-input
          v-model="ruleForm.sectionTitle"
          placeholder="请输入标题"
        ></el-input>
      </el-form-item>
      <el-form-item prop="prText" style="width: 100%">
        <div style="width: 100%">
          <!-- <ClassicEditor
            v-model="Tryimg"
            ref="editorRef"
            style="width: 100%"
          ></ClassicEditor> -->
          <ClassicEditor
            v-model="ruleForm.prText"
            ref="editorRef"
            style="width: 100%"
          ></ClassicEditor>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>
.text-content-wrapper {
  margin-bottom: 20px;
  width: 100%;
}
</style>
