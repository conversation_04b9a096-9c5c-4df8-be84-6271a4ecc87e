<script setup lang="ts">
import textContent from "./textContent.vue";
import videoContent from "./videoContent.vue";

import { emitter } from "@/utils/emitter";
import { Event } from "@/types/event";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { prjForm } from "@/utils/constant";

const props = defineProps({
  prjId: Number,
  curPrjForm: {
    type: Number,
    required: true,
  },
  chapterList: {
    type: Array,
    required: true,
  },
  releaseDate: {
    type: String,
    required: false,
  },
});
console.log("SingleContentWrapper - releaseDate:", props.releaseDate);
const curChapterId = ref(-1);
const curPrjForm = ref(-1);
const chapter = ref<any>();

watch(
  () => props,
  (newVal) => {
    if (newVal) {
      if (props.chapterList.length > 0) {
        chapter.value = props.chapterList[0];
        curChapterId.value = chapter.value.sectionId;
        curPrjForm.value = props.curPrjForm;
      }
    }
  },
  { deep: true, immediate: true }
);

// 处理保存完成
const handleSaveDone = async (data: { status: number; chapterId: number }) => {
  if (data.status === 2 || data.status === 3) {
    // 2: 提交/3: 大的存草稿
    emitter.emit(Event.SAVE_DRAFT_DONE, data.status == 2 ? true : false);
  } else if (data.status === 0) {
    // 0: 存草稿
    curChapterId.value = -1;
    // ElMessage.success("存草稿")
  }
};
const handleSave = (submit?: Boolean) => {
  if (curChapterId.value === -1) {
    emitter.emit(Event.SAVE_DRAFT_DONE, submit ?? false);
    return;
  }
  emitter.emit(Event.SAVE_CHAPTER, {
    status: submit ? 2 : 3,
    id: curChapterId.value,
  });
};

onMounted(() => {
  emitter.on(Event.SAVE_DRAFT, handleSave);
});
onUnmounted(() => {
  emitter.off(Event.SAVE_DRAFT, handleSave);
});
</script>
<template>
  <div class="chapter-wrapper">
    <div v-if="props.curPrjForm == prjForm.video">
      <video-content
        :chapter="chapter"
        :show-title="false"
        :project-id="props.prjId"
        @save:chapter="handleSaveDone"
        :release-date="chapter?.releaseDate || props.releaseDate"
      >
      </video-content>
    </div>
    <div v-else="props.curPrjForm == prjForm.text">
      <text-content
        :chapter="chapter"
        :show-title="false"
        :project-id="props.prjId"
        @save:chapter="handleSaveDone"
      ></text-content>
    </div>
  </div>
</template>
<style scoped>
.chapter-wrapper {
  width: 100%;

  :deep(.el-collapse-item__header) {
    height: 100%;
    margin-bottom: 10px;
  }

  .collapse-title {
    font-family: var(--font-family-text);
    display: flex;
    width: 100%;
    justify-content: space-between;
    height: 35px;
    line-height: 35px;
    background-color: var(--color-primary);
    color: white;
    padding: 0 10px;
    cursor: default;

    .btns {
      display: flex;
      width: 88px;
      justify-content: space-between;

      .b {
        cursor: pointer;
      }
    }
  }

  :deep(.el-collapse-item__arrow) {
    display: none;
  }
}
.add-section-wrapper {
  width: 100%;
  margin-bottom: 20px;
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  text-align: center;
  align-items: center;
  border: 2px dashed var(--color-primary);
}
</style>
