import { http } from "@/apis";
import type { APIResponse } from "@/utils/type";

// 获取项目列表（分页）
export interface key2prjList {
  current: number;
  limit: number;
  prjForm?: string;
  title?: string;
  status?: string;
  prjType?: string;
  // releaseTime?: string;
  // sort?: number;
}
export function getProjectList(param: key2prjList): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/tprj/getMyPrj",
    data: param,
  });
}

export function deleteProject(param?: number[]): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/tprj/delete",
    data: param,
  });
}
export function withdrawProject(param?: number[]): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/tprj/back",
    data: param,
  });
}
// 获取我的项目审核记录
export function getMyPrjRecordApi(id: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/process/record/query?projectId=${id}`,
  });
}
